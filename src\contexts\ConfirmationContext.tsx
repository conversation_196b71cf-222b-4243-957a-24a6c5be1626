"use client";
import React, { createContext, useContext } from 'react';
import { useAppSettings } from './AppSettingsContext';

interface ConfirmationContextType {
  requireConfirmation: boolean;
  toggleConfirmation: () => void;
}

const ConfirmationContext = createContext<ConfirmationContextType | undefined>(undefined);

export const useConfirmation = () => {
  const context = useContext(ConfirmationContext);
  if (context === undefined) {
    throw new Error('useConfirmation must be used within a ConfirmationProvider');
  }
  return context;
};

interface ConfirmationProviderProps {
  children: React.ReactNode;
}

export const ConfirmationProvider: React.FC<ConfirmationProviderProps> = ({ children }) => {
  const { settings, toggleConfirmation } = useAppSettings();

  const value = {
    requireConfirmation: settings.requireConfirmation,
    toggleConfirmation
  };

  return (
    <ConfirmationContext.Provider value={value}>
      {children}
    </ConfirmationContext.Provider>
  );
};
