// app/api/ai-analysis-history/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

interface AnalysisHistoryItem {
  id: string;
  botName: string;
  timestamp: string;
  response: {
    error: boolean;
    message: string;
    symbol: string;
    timeframes: string[];
    ai_provider: string;
    analysis: string;
    custom_prompt: string;
    image_analyzed: boolean;
    use_signal_format: number;
    timestamp: string;
    signal_data?: {
      signal_id: string;
      symbol: string;
      signal_type: string;
      entry_price: string;
      sl_price: string;
      tp1_price: string;
      tp2_price: string;
      tp3_price: string;
      tp4_price?: string;
      tp5_price?: string;
    };
    structured_signal: boolean;
  };
}

const CONFIG_DIR = path.join(process.cwd(), 'config');
const ANALYSIS_HISTORY_FILE = path.join(CONFIG_DIR, 'ai-analysis-history.json');
const LEGACY_ANALYSIS_HISTORY_FILE = path.join(process.cwd(), 'ai-analysis-history.json');

// Ensure config directory exists
async function ensureConfigDir() {
  try {
    await fs.access(CONFIG_DIR);
  } catch {
    await fs.mkdir(CONFIG_DIR, { recursive: true });
  }
}

async function loadHistory(): Promise<AnalysisHistoryItem[]> {
  try {
    await ensureConfigDir();

    // Try to load from config folder first
    try {
      const data = await fs.readFile(ANALYSIS_HISTORY_FILE, 'utf8');
      return JSON.parse(data);
    } catch {
      // Fallback to legacy location
      try {
        const data = await fs.readFile(LEGACY_ANALYSIS_HISTORY_FILE, 'utf8');
        const history = JSON.parse(data);
        // Migrate to new location
        await saveHistory(history);
        // Remove legacy file
        await fs.unlink(LEGACY_ANALYSIS_HISTORY_FILE).catch(() => {});
        return history;
      } catch {
        return [];
      }
    }
  } catch {
    return [];
  }
}

async function saveHistory(history: AnalysisHistoryItem[]): Promise<void> {
  await ensureConfigDir();
  // Keep only the last 1000 entries to prevent file from growing too large
  const limitedHistory = history.slice(0, 1000);
  await fs.writeFile(ANALYSIS_HISTORY_FILE, JSON.stringify(limitedHistory, null, 2));
}

export async function GET() {
  try {
    const history = await loadHistory();
    return NextResponse.json(history);
  } catch (error) {
    console.error('Failed to load analysis history:', error);
    return NextResponse.json({ error: 'Failed to load analysis history' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const history: AnalysisHistoryItem[] = await request.json();
    await saveHistory(history);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to save analysis history:', error);
    return NextResponse.json({ error: 'Failed to save analysis history' }, { status: 500 });
  }
}
