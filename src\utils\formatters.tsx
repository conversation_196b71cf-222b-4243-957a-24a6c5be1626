// utils/formatters.tsx
import React from 'react';
import { formatNumber, USD_TO_THB_RATE } from '@/config/global';

// Format profit summary with USD and optional THB (React component)
export const formatProfitLoss = (
  totalProfit: number,
  style: number = 0,
  showThb: boolean = true,
  exchangeRate: number = USD_TO_THB_RATE
) => {
  const color = totalProfit >= 0 ? "text-green" : "text-red";
  const sign = totalProfit >= 0 ? "+" : "";
  const thbAmount = totalProfit * exchangeRate;

  if (style == 0) {
    return (
      <span className={`text-nowrap`}>
        <span className={`${color}-400`}>{sign}{formatNumber(totalProfit)}</span>
        {showThb && (
          <>
            <br />
            <span className={`${color}-200 text-xs`}>({sign}฿{formatNumber(thbAmount, 0)})</span>
          </>
        )}
      </span>
    );
  } else {
    return (
      <span className={`text-nowrap `}>
        <span className={`${color}-400`}>{sign}{formatNumber(totalProfit)}</span>
        {showThb && (
          <span className={`${color}-200 text-xs ml-1`}>({sign}฿{formatNumber(thbAmount, 0)})</span>
        )}
      </span>
    );
  }
};
