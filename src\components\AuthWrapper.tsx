"use client";
import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { ConfirmationProvider } from '@/contexts/ConfirmationContext';
import { AppSettingsProvider } from '@/contexts/AppSettingsContext';
import LoginScreen from './LoginScreen';

import AppSettings from './AppSettings';

interface AuthWrapperProps {
  children: React.ReactNode;
}

const AuthWrapper: React.FC<AuthWrapperProps> = ({ children }) => {
  const { isAuthenticated, loading, logout } = useAuth();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  // Show login screen if not authenticated
  if (!isAuthenticated) {
    return <LoginScreen />;
  }

  // Show app with logout button if authenticated
  return (
    <AppSettingsProvider>
      <ConfirmationProvider>
        <div className="min-h-screen bg-gray-900">
        {/* Top navigation bar with confirmation toggle and logout */}
        <nav className="bg-gray-800 border-b border-gray-700 px-4 py-3">
          <div className="flex justify-between items-center xxmax-w-7xl mx-auto">
            <div className="flex items-center space-x-6">
              <div className="flex items-center">
                <span className="text-green-400 text-lg">🛡️</span>
                <span className="text-white ml-2 font-medium">Secure Session Active</span>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* App Settings */}
              <AppSettings />

              <button
                onClick={logout}
                className="flex items-center px-3 py-1.5 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                <span className="mr-1">🔓</span>
                Logout
              </button>
            </div>
          </div>
        </nav>

          {/* Main app content */}
          <main className="xxmax-w-7xl mx-auto">
            {children}
          </main>
        </div>
      </ConfirmationProvider>
    </AppSettingsProvider>
  );
};

export default AuthWrapper;
