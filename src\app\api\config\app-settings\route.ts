import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

const CONFIG_DIR = path.join(process.cwd(), 'config');
const SETTINGS_FILE = path.join(CONFIG_DIR, 'app-settings.json');

// Ensure config directory exists
async function ensureConfigDir() {
  try {
    await fs.access(CONFIG_DIR);
  } catch {
    await fs.mkdir(CONFIG_DIR, { recursive: true });
  }
}

export async function GET() {
  try {
    await ensureConfigDir();
    
    try {
      const data = await fs.readFile(SETTINGS_FILE, 'utf8');
      const settings = JSON.parse(data);
      return NextResponse.json(settings);
    } catch (error) {
      // File doesn't exist, return default settings
      const defaultSettings = {
        showTabNavigation: true,
        requireConfirmation: true,
        magicNumber: 50,
        visibleTabs: {
          instant: true,
          input: true,
          control: true,
          calendar: false,
          discord: false,
          aibot: true,
          orders: true,
        },
        currency: {
          showThb: true,
          usdToThbRate: 32.5,
        },
        orders: {
          showCloseColumn: true,
        },
      };
      return NextResponse.json(defaultSettings);
    }
  } catch (error) {
    console.error('Error loading app settings:', error);
    return NextResponse.json(
      { error: 'Failed to load app settings' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await ensureConfigDir();
    
    const settings = await request.json();
    
    // Validate settings structure
    if (!settings || typeof settings !== 'object') {
      return NextResponse.json(
        { error: 'Invalid settings format' },
        { status: 400 }
      );
    }
    
    // Save settings to file
    await fs.writeFile(SETTINGS_FILE, JSON.stringify(settings, null, 2));
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error saving app settings:', error);
    return NextResponse.json(
      { error: 'Failed to save app settings' },
      { status: 500 }
    );
  }
}
