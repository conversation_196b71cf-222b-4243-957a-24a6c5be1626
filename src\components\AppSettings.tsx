// components/AppSettings.tsx
"use client";
import React, { useState } from 'react';
import { useAppSettings } from '@/contexts/AppSettingsContext';

export default function AppSettings() {
  const { settings, toggleTabVisibility, toggleTabNavigation, toggleConfirmation, updateMagicNumber, toggleThbDisplay, updateExchangeRate, toggleCloseColumn } = useAppSettings();
  const [showSettings, setShowSettings] = useState(false);
  const [tempExchangeRate, setTempExchangeRate] = useState(settings.currency.usdToThbRate.toString());
  const [saveStatus, setSaveStatus] = useState<string>('');

  // Update tempExchangeRate when settings change
  React.useEffect(() => {
    setTempExchangeRate(settings.currency.usdToThbRate.toString());
  }, [settings.currency.usdToThbRate]);

  const tabLabels = {
    instant: 'Instant Tab',
    input: 'Input Tab',
    control: 'Control Tab',
    calendar: 'Calendar Tab',
    discord: 'Discord Bot Tab',
    aibot: 'AI Bot Tab',
    orders: 'Orders Tab',
  };

  const handleExchangeRateChange = (value: string) => {
    setTempExchangeRate(value);
    const rate = parseFloat(value);
    if (!isNaN(rate) && rate > 0) {
      updateExchangeRate(rate);
    }
  };

  const handleSaveSettings = async () => {
    try {
      setSaveStatus('Saving...');
      const response = await fetch('/api/config/app-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        setSaveStatus('✅ Settings saved successfully');
      } else {
        setSaveStatus('❌ Failed to save settings');
      }

      // Clear status after 2 seconds
      setTimeout(() => setSaveStatus(''), 2000);
    } catch (error) {
      setSaveStatus('❌ Save error');
      setTimeout(() => setSaveStatus(''), 2000);
    }
  };

  return (
    <div className="relative">
      <button
        onClick={() => setShowSettings(!showSettings)}
        className="flex items-center space-x-2 px-3 py-2 text-gray-300 hover:text-white transition-colors"
        title="App Settings"
      >
        <span className="text-lg">⚙️</span>
        <span className="hidden sm:inline">Settings</span>
      </button>

      {showSettings && (
        <div className="absolute right-0 top-full mt-2 w-96 bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-50">
          <div className="p-4">
            <h3 className="text-lg font-semibold text-white mb-4">App Settings</h3>
            
            {/* Currency Settings */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-200 mb-2">Currency Display</h4>
              <div className="space-y-3">
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={settings.currency.showThb}
                    onChange={toggleThbDisplay}
                    className="rounded"
                  />
                  <span className="text-gray-200">Show Thai Baht (THB)</span>
                </label>

                <div className="flex items-center space-x-3">
                  <label className="text-sm text-gray-200 min-w-fit">USD to THB Rate:</label>
                  <input
                    type="number"
                    value={tempExchangeRate}
                    onChange={(e) => handleExchangeRateChange(e.target.value)}
                    className="flex-1 px-2 py-1 bg-gray-700 text-white border border-gray-600 rounded text-sm"
                    step="0.1"
                    min="0"
                    placeholder="32.5"
                  />
                </div>
                <p className="text-xs text-gray-400">
                  Exchange rate for converting USD amounts to THB
                </p>
              </div>
            </div>

            {/* Orders Settings */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-200 mb-2">Orders Display</h4>
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={settings.orders.showCloseColumn}
                  onChange={toggleCloseColumn}
                  className="rounded"
                />
                <span className="text-gray-200">Show Close/Cancel Column</span>
              </label>
              <p className="text-xs text-gray-400 mt-1">
                Show or hide the close/cancel action column in order tables
              </p>
            </div>

            {/* Tab Navigation Toggle */}
            <div className="mb-4">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={settings.showTabNavigation}
                  onChange={toggleTabNavigation}
                  className="rounded"
                />
                <span className="text-gray-200">Show Tab Navigation</span>
              </label>
              <p className="text-xs text-gray-400 mt-1">
                Hide tab navigation when presenting to others
              </p>
            </div>

            {/* Confirmation Toggle */}
            <div className="mb-4">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={settings.requireConfirmation}
                  onChange={toggleConfirmation}
                  className="rounded"
                />
                <span className="text-gray-200">Require Confirmation</span>
              </label>
              <p className="text-xs text-gray-400 mt-1">
                {settings.requireConfirmation
                  ? 'Show confirmation dialog before sending orders ✅'
                  : 'Send orders instantly without confirmation ⚡'
                }
              </p>
            </div>

            {/* Individual Tab Visibility */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-200 mb-2">Visible Tabs</h4>
              <div className="space-y-2">
                {Object.entries(settings.visibleTabs).map(([tab, visible]) => (
                  <label key={tab} className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={visible}
                      onChange={() => toggleTabVisibility(tab as keyof typeof settings.visibleTabs)}
                      className="rounded"
                      disabled={!settings.showTabNavigation}
                    />
                    <span className={`text-sm ${settings.showTabNavigation ? 'text-gray-200' : 'text-gray-500'}`}>
                      {tabLabels[tab as keyof typeof tabLabels]}
                    </span>
                  </label>
                ))}
              </div>
              {!settings.showTabNavigation && (
                <p className="text-xs text-gray-500 mt-2">
                  Enable tab navigation to configure individual tabs
                </p>
              )}
            </div>

            {/* Save Settings */}
            <div className="border-t border-gray-600 pt-4 mb-4">
              <div className="space-y-2">
                <button
                  onClick={handleSaveSettings}
                  className="w-full px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors"
                  disabled={saveStatus.includes('Saving')}
                >
                  💾 Save Settings
                </button>
                {saveStatus && (
                  <p className="text-xs text-gray-300 text-center">
                    {saveStatus}
                  </p>
                )}
              </div>
            </div>

            {/* Security Info */}
            {/* <div className="border-t border-gray-600 pt-4">
              <h4 className="text-sm font-medium text-gray-200 mb-2">Security Features</h4>
              <div className="text-xs text-gray-400 space-y-1">
                <p>• Access token authentication for webhooks</p>
                <p>• Secure Discord bot token handling</p>
                <p>• Tab visibility controls for presentations</p>
                <p>• Settings stored in config/app-settings.json</p>
              </div>
            </div> */}

            {/* Close Button */}
            <div className="flex justify-end mt-4">
              <button
                onClick={() => setShowSettings(false)}
                className="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
