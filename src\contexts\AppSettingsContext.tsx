// contexts/AppSettingsContext.tsx
"use client";
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface AppSettings {
  showTabNavigation: boolean;
  requireConfirmation: boolean;
  magicNumber: number;
  visibleTabs: {
    instant: boolean;
    input: boolean;
    control: boolean;
    calendar: boolean;
    discord: boolean;
    aibot: boolean;
    orders: boolean;
  };
  currency: {
    showThb: boolean;
    usdToThbRate: number;
  };
  orders: {
    showCloseColumn: boolean;
  };
}

interface AppSettingsContextType {
  settings: AppSettings;
  updateSettings: (newSettings: Partial<AppSettings>) => void;
  toggleTabVisibility: (tab: keyof AppSettings['visibleTabs']) => void;
  toggleTabNavigation: () => void;
  toggleConfirmation: () => void;
  updateMagicNumber: (value: number) => void;
  toggleThbDisplay: () => void;
  updateExchangeRate: (rate: number) => void;
  toggleCloseColumn: () => void;
}

const defaultSettings: AppSettings = {
  showTabNavigation: true,
  requireConfirmation: true,
  magicNumber: 50,
  visibleTabs: {
    instant: true,
    input: true,
    control: true,
    calendar: false,
    discord: false,
    aibot: true,
    orders: true,
  },
  currency: {
    showThb: true,
    usdToThbRate: 32.5,
  },
  orders: {
    showCloseColumn: true,
  },
};

const AppSettingsContext = createContext<AppSettingsContextType | undefined>(undefined);

export function AppSettingsProvider({ children }: { children: ReactNode }) {
  const [settings, setSettings] = useState<AppSettings>(defaultSettings);

  // Load settings from config file on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        // Try to load from config file first
        const response = await fetch('/api/config/app-settings');
        if (response.ok) {
          const parsed = await response.json();
          setSettings({ ...defaultSettings, ...parsed });
        } else {
          // Fallback to localStorage for backward compatibility
          const savedSettings = localStorage.getItem('app-settings');
          if (savedSettings) {
            const parsed = JSON.parse(savedSettings);
            setSettings({ ...defaultSettings, ...parsed });
            // Migrate to config file
            await saveSettingsToFile({ ...defaultSettings, ...parsed });
          }
        }
      } catch (error) {
        console.error('Failed to load app settings:', error);
        // Fallback to localStorage
        try {
          const savedSettings = localStorage.getItem('app-settings');
          if (savedSettings) {
            const parsed = JSON.parse(savedSettings);
            setSettings({ ...defaultSettings, ...parsed });
          }
        } catch (localError) {
          console.error('Failed to load from localStorage:', localError);
        }
      }
    };

    loadSettings();
  }, []);

  // Save settings to config file whenever they change
  useEffect(() => {
    if (settings !== defaultSettings) {
      saveSettingsToFile(settings);
    }
  }, [settings]);

  const saveSettingsToFile = async (settingsToSave: AppSettings) => {
    try {
      await fetch('/api/config/app-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settingsToSave),
      });
      // Also save to localStorage as backup
      localStorage.setItem('app-settings', JSON.stringify(settingsToSave));
    } catch (error) {
      console.error('Failed to save app settings to file:', error);
      // Fallback to localStorage only
      try {
        localStorage.setItem('app-settings', JSON.stringify(settingsToSave));
      } catch (localError) {
        console.error('Failed to save to localStorage:', localError);
      }
    }
  };

  const updateSettings = (newSettings: Partial<AppSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const toggleTabVisibility = (tab: keyof AppSettings['visibleTabs']) => {
    setSettings(prev => ({
      ...prev,
      visibleTabs: {
        ...prev.visibleTabs,
        [tab]: !prev.visibleTabs[tab],
      },
    }));
  };

  const toggleTabNavigation = () => {
    setSettings(prev => ({
      ...prev,
      showTabNavigation: !prev.showTabNavigation,
    }));
  };

  const toggleConfirmation = () => {
    setSettings(prev => ({
      ...prev,
      requireConfirmation: !prev.requireConfirmation,
    }));
  };

  const updateMagicNumber = (value: number) => {
    setSettings(prev => ({
      ...prev,
      magicNumber: value,
    }));
  };

  const toggleThbDisplay = () => {
    setSettings(prev => ({
      ...prev,
      currency: {
        ...prev.currency,
        showThb: !prev.currency.showThb,
      },
    }));
  };

  const updateExchangeRate = (rate: number) => {
    setSettings(prev => ({
      ...prev,
      currency: {
        ...prev.currency,
        usdToThbRate: rate,
      },
    }));
  };

  const toggleCloseColumn = () => {
    setSettings(prev => ({
      ...prev,
      orders: {
        ...prev.orders,
        showCloseColumn: !prev.orders.showCloseColumn,
      },
    }));
  };

  return (
    <AppSettingsContext.Provider value={{
      settings,
      updateSettings,
      toggleTabVisibility,
      toggleTabNavigation,
      toggleConfirmation,
      updateMagicNumber,
      toggleThbDisplay,
      updateExchangeRate,
      toggleCloseColumn,
    }}>
      {children}
    </AppSettingsContext.Provider>
  );
}

export function useAppSettings() {
  const context = useContext(AppSettingsContext);
  if (context === undefined) {
    throw new Error('useAppSettings must be used within an AppSettingsProvider');
  }
  return context;
}
