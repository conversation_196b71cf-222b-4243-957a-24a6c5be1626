// services/discordService.ts
import { DiscordBotConfig, DiscordMessage, DEFAULT_PATTERNS } from '@/types/discord';
import { LOT_DISTRIBUTION_CONFIG, mapActionToValid } from '@/config/global';
import { getOrGenerateSignalId } from '@/utils/signalId';

class DiscordService {
  private static instance: DiscordService;
  private bots: Map<string, any> = new Map();
  private configs: Map<string, DiscordBotConfig> = new Map();

  private constructor() {}

  static getInstance(): DiscordService {
    if (!DiscordService.instance) {
      DiscordService.instance = new DiscordService();
    }
    return DiscordService.instance;
  }

  async startBot(config: DiscordBotConfig, token: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Only run on server side
      if (typeof window !== 'undefined') {
        return { success: false, error: 'Discord bots can only run on server side' };
      }

      // Check if discord.js is available
      let Client, GatewayIntentBits;
      try {
        const discord = await import('discord.js');
        Client = discord.Client;
        GatewayIntentBits = discord.GatewayIntentBits;
      } catch (importError) {
        console.error('Failed to import discord.js:', importError);
        return { success: false, error: 'Discord.js not available' };
      }

      if (this.bots.has(config.id)) {
        await this.stopBot(config.id);
      }

      const client = new Client({
        intents: [
          GatewayIntentBits.Guilds,
          GatewayIntentBits.GuildMessages,
          GatewayIntentBits.MessageContent,
        ],
      });

      client.on('ready', () => {
        console.log(`Discord bot ${config.name} is ready!`);
      });

      client.on('messageCreate', async (message: any) => {
        if (message.author.bot) return;
        if (message.guildId !== config.serverId) return;
        if (message.channelId !== config.channelId) return;

        const discordMessage: DiscordMessage = {
          id: message.id,
          content: message.content,
          author: {
            id: message.author.id,
            username: message.author.username,
            discriminator: message.author.discriminator,
          },
          timestamp: message.createdAt.toISOString(),
          channelId: message.channelId,
          serverId: message.guildId || '',
        };

        await this.processMessage(config, discordMessage);
      });

      client.on('error', (error: any) => {
        console.error(`Discord bot ${config.name} error:`, error);
      });

      await client.login(token);
      this.bots.set(config.id, client);
      this.configs.set(config.id, config);
      
      return { success: true };
    } catch (error: any) {
      console.error(`Failed to start Discord bot ${config.name}:`, error);
      return { success: false, error: error.message };
    }
  }

  async stopBot(botId: string): Promise<void> {
    const client = this.bots.get(botId);
    if (client) {
      await client.destroy();
      this.bots.delete(botId);
      this.configs.delete(botId);
    }
  }

  private async processMessage(config: DiscordBotConfig, message: DiscordMessage): Promise<void> {
    try {
      console.log(`Processing message from ${message.author.username}: ${message.content}`);
      
      // Use the parseLongText function logic here
      const parsedData = await this.parseLongText(message.content, config);
      
      if (parsedData) {
        console.log('Parsed data:', parsedData);
        // Send to webhook
        await this.sendToWebhook(config.webhookUrl, parsedData, config);
      } else {
        console.log('No valid trading data found in message');
      }
    } catch (error) {
      console.error(`Error processing message from bot ${config.name}:`, error);
    }
  }

  private async loadLotConfig(): Promise<any> {
    // Use the global config directly - no need to load from JSON file
    return LOT_DISTRIBUTION_CONFIG;
  }

  private async parseLongText(content: string, config: DiscordBotConfig): Promise<any | null> {
    const lines = content.split("\n").map((line) => line.trim());
    const rows: { tp: number; lot: number }[] = [];

    // Extract or generate Signal ID
    const signalId = getOrGenerateSignalId(content);

    let data = {
      a: "Buy Limit",
      p: 0.0,
      sl: 0.0,
      s: "XAUUSD",
      c: "ZD_INPUT",
      id: signalId,
      reason: "",
      risk: "",
    };

    let hasValidData = false;
    const patterns = config.patterns || DEFAULT_PATTERNS;

    lines.forEach((line) => {
      // TP Pattern
      if (line.toLowerCase().startsWith("tp")) {
        const match = line.match(new RegExp(patterns.tp, 'i'));
        if (match) {
          rows.push({ tp: parseFloat(match[1]), lot: 0.01 }); // Lot will be calculated later
          hasValidData = true;
        }
      }

      // SL Pattern
      const slMatch = line.match(new RegExp(patterns.sl, 'i'));
      if (slMatch) {
        data.sl = parseFloat(slMatch[1]);
        hasValidData = true;
      }

      // Symbol Pattern
      const symbolMatch = line.match(new RegExp(patterns.symbol, 'i'));
      if (symbolMatch) {
        data.s = symbolMatch[1].toUpperCase();
        hasValidData = true;
      }

      // Signal Pattern
      const signalMatch = line.match(new RegExp(patterns.signal, 'i'));
      if (signalMatch) {
        const rawAction = signalMatch[1].trim();
        data.a = mapActionToValid(rawAction);
        hasValidData = true;
      }

      // Price Pattern
      const priceMatch = line.match(new RegExp(patterns.price, 'i'));
      if (priceMatch) {
        const start = parseFloat(priceMatch[1]);
        const end = parseFloat(priceMatch[3]);
        const mid = (start + end) / 2;
        data.p = parseFloat(mid.toFixed(2));
        hasValidData = true;
      }

      // Comment Pattern
      const commentMatch = line.match(new RegExp(patterns.comment, 'i'));
      if (commentMatch) {
        // data.c = `ZD_${commentMatch[1]}_${signalId}`;
        data.c = `ZD_${commentMatch[1].slice(0, 3)}`;
        hasValidData = true;
      }

      // Reason Pattern
      const reasonMatch = line.match(/Reason\s*:\s*(.+)/i);
      if (reasonMatch) {
        data.reason = reasonMatch[1].trim();
        hasValidData = true;
      }

      // Risk Pattern
      const riskMatch = line.match(/Risk\s*:\s*(.+)/i);
      if (riskMatch) {
        data.risk = riskMatch[1].trim();
        hasValidData = true;
      }
    });

    // Set default comment with Signal ID if no comment pattern was found
    if (data.c === "ZD_INPUT") {
      data.c = `ZD_DISCORD_${signalId}`;
    }

    if (hasValidData && rows.length > 0) {
      return {
        rows: await this.calculateLots(rows, config),
        ...data
      };
    }

    return null;
  }

  private async calculateLots(tpRows: { tp: number; lot: number }[], config: DiscordBotConfig): Promise<{ tp: number; lot: number }[]> {
    const lotConfig = await this.loadLotConfig();
    const maxLot = config.lotSettings?.maxLotPerMessage || lotConfig.maxLotPerMessage;
    const tpCount = tpRows.length;

    // Get distribution percentages for this TP count
    const distribution = config.lotSettings?.lotDistribution[tpCount] ||
                        lotConfig.lotDistribution[tpCount.toString()] ||
                        [100]; // Fallback to 100% if not configured

    return tpRows.map((row, index) => {
      const percentage = distribution[index] || 1; // Fallback to 1% if index exceeds distribution array
      let calculatedLot = (maxLot * percentage) / 100;

      // Ensure minimum lot size
      if (calculatedLot < (lotConfig.minLotSize || 0.01)) {
        calculatedLot = lotConfig.minLotSize || 0.01;
      }

      return {
        tp: row.tp,
        lot: parseFloat(calculatedLot.toFixed(2))
      };
    });
  }

  private async sendToWebhook(webhookUrl: string, data: any, config?: DiscordBotConfig): Promise<void> {
    try {
      console.log(`Sending to webhook: ${webhookUrl}`);

      // Get access token from environment variables
      const accessToken = process.env.WEBHOOK_ACCESS_TOKEN;

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add access token to headers if available
      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
        headers['X-Access-Token'] = accessToken;
      }

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          ...data,
          // Add metadata
          _meta: {
            source: 'discord_bot',
            botId: config?.id,
            botName: config?.name,
            timestamp: new Date().toISOString(),
            accessToken: accessToken ? 'provided' : 'not_provided'
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`Webhook request failed: ${response.status}`);
      }

      console.log('Successfully sent data to webhook:', webhookUrl);
    } catch (error) {
      console.error('Failed to send data to webhook:', error);
      throw error;
    }
  }

  getBotStatus(botId: string): boolean {
    const client = this.bots.get(botId);
    return client && client.isReady();
  }

  getAllBots(): DiscordBotConfig[] {
    return Array.from(this.configs.values());
  }

  generateInviteLink(clientId: string, permissions: string[]): string {
    const permissionMap: Record<string, string> = {
      'VIEW_CHANNEL': '1024',
      'READ_MESSAGE_HISTORY': '65536',
      'SEND_MESSAGES': '2048',
      'MANAGE_MESSAGES': '8192',
      'EMBED_LINKS': '16384',
      'ATTACH_FILES': '32768',
      'USE_EXTERNAL_EMOJIS': '262144',
      'ADD_REACTIONS': '64',
      'MENTION_EVERYONE': '131072',
      'MANAGE_WEBHOOKS': '536870912',
      'USE_SLASH_COMMANDS': '2147483648'
    };

    let permissionValue = 0;
    permissions.forEach(permission => {
      if (permissionMap[permission]) {
        permissionValue += parseInt(permissionMap[permission]);
      }
    });

    return `https://discord.com/api/oauth2/authorize?client_id=${clientId}&permissions=${permissionValue}&scope=bot`;
  }
}

export const discordService = DiscordService.getInstance();
