// app/api/discord/bots/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { DiscordBotConfig } from '@/types/discord';
// Import discordService only when needed to avoid build issues
// import { discordService } from '@/services/discordService';

const CONFIG_DIR = path.join(process.cwd(), 'config');
const BOTS_CONFIG_FILE = path.join(CONFIG_DIR, 'discord-bots.json');
const LEGACY_BOTS_CONFIG_FILE = path.join(process.cwd(), 'discord-bots.json');

// Ensure config directory exists
async function ensureConfigDir() {
  try {
    await fs.access(CONFIG_DIR);
  } catch {
    await fs.mkdir(CONFIG_DIR, { recursive: true });
  }
}

async function loadBots(): Promise<DiscordBotConfig[]> {
  try {
    await ensureConfigDir();

    // Try to load from config folder first
    try {
      const data = await fs.readFile(BOTS_CONFIG_FILE, 'utf8');
      return JSON.parse(data);
    } catch {
      // Fallback to legacy location
      try {
        const data = await fs.readFile(LEGACY_BOTS_CONFIG_FILE, 'utf8');
        const bots = JSON.parse(data);
        // Migrate to new location
        await saveBots(bots);
        // Remove legacy file
        await fs.unlink(LEGACY_BOTS_CONFIG_FILE).catch(() => {});
        return bots;
      } catch {
        // File doesn't exist or is invalid, return empty array
        return [];
      }
    }
  } catch {
    return [];
  }
}

async function saveBots(bots: DiscordBotConfig[]): Promise<void> {
  await ensureConfigDir();
  await fs.writeFile(BOTS_CONFIG_FILE, JSON.stringify(bots, null, 2));
}

export async function GET() {
  try {
    const bots = await loadBots();
    // For now, just return bots without runtime status to avoid build issues
    // TODO: Add runtime status check when discord service is properly initialized
    return NextResponse.json(bots);
  } catch (error) {
    console.error('Failed to load Discord bots:', error);
    return NextResponse.json({ error: 'Failed to load bots' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const bots: DiscordBotConfig[] = await request.json();
    await saveBots(bots);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to save Discord bots:', error);
    return NextResponse.json({ error: 'Failed to save bots' }, { status: 500 });
  }
}
