// app/api/ai-analysis/route.ts
import { NextRequest, NextResponse } from 'next/server';

interface ChartDataPoint {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  ema_20: number;
  rsi_14: number;
  macd: number;
  macd_signal: number;
  rsi_25: number;
  sma50_rsi25: number;
  rsi_50: number;
  sma25_rsi50: number;
}

interface AnalysisRequest {
  botId: string;
  aiProvider: "gpt" | "gemini";
  chartData: {
    symbol: string;
    timeframe: string;
    bars_count: number;
    data: ChartDataPoint[];
  } | {
    [timeframe: string]: {
      symbol: string;
      timeframe: string;
      bars_count: number;
      data: ChartDataPoint[];
    };
  };
  userPrompt: string;
  uploadedImage?: string | null;
  isMultiTimeframe?: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const body: AnalysisRequest = await request.json();
    const { aiProvider, chartData, userPrompt, uploadedImage, isMultiTimeframe } = body;

    // Prepare the analysis prompt with chart data
    let chartDataSummary = '';
    let symbol = '';

    if (isMultiTimeframe && typeof chartData === 'object' && !('symbol' in chartData)) {
      // Multi-timeframe data
      const multiData = chartData as { [timeframe: string]: any };
      const firstTimeframe = Object.values(multiData)[0];
      symbol = firstTimeframe?.symbol || 'UNKNOWN';

      chartDataSummary = `MULTI-TIMEFRAME ANALYSIS:
${Object.entries(multiData).map(([tf, data]) => `
${tf} Timeframe:
- Bars: ${data.bars_count}
- Latest Price: ${data.data[data.data.length - 1]?.close || 'N/A'}
- EMA 20: ${data.data[data.data.length - 1]?.ema_20 || 'N/A'}
- RSI 14: ${data.data[data.data.length - 1]?.rsi_14 || 'N/A'}
- MACD: ${data.data[data.data.length - 1]?.macd || 'N/A'}
- MACD Signal: ${data.data[data.data.length - 1]?.macd_signal || 'N/A'}`).join('\n')}`;
    } else {
      // Single timeframe data
      const singleData = chartData as any;
      symbol = singleData?.symbol || 'UNKNOWN';
      chartDataSummary = `Chart Data Summary:
- Symbol: ${singleData.symbol}
- Timeframe: ${singleData.timeframe}
- Bars: ${singleData.bars_count}
- Latest Price: ${singleData.data[singleData.data.length - 1]?.close || 'N/A'}

Technical Indicators (Latest):
- EMA 20: ${singleData.data[singleData.data.length - 1]?.ema_20 || 'N/A'}
- RSI 14: ${singleData.data[singleData.data.length - 1]?.rsi_14 || 'N/A'} (Overbought >70, Oversold <30)
- MACD: ${singleData.data[singleData.data.length - 1]?.macd || 'N/A'}
- MACD Signal: ${singleData.data[singleData.data.length - 1]?.macd_signal || 'N/A'}
- RSI 25: ${singleData.data[singleData.data.length - 1]?.rsi_25 || 'N/A'}
- RSI 50: ${singleData.data[singleData.data.length - 1]?.rsi_50 || 'N/A'}`;
    }

    const systemPrompt = `You are a professional forex trading analyst with 15+ years of experience. Analyze the provided chart data and give precise trading recommendations.

CRITICAL ANALYSIS REQUIREMENTS:
1. Consider multiple timeframe confluence ${isMultiTimeframe ? '(YOU HAVE MULTIPLE TIMEFRAMES - ANALYZE CONFLUENCE!)' : ''}
2. Analyze trend direction, momentum, and reversal signals
3. Evaluate support/resistance levels
4. Assess risk-reward ratios (minimum 1:2)
5. Consider market volatility and session times
6. Factor in fundamental analysis context

${chartDataSummary}

MANDATORY OUTPUT FORMAT (EXACT FORMAT REQUIRED):
Signal: [Buy Now/Sell Now/Buy Limit/Sell Limit/Buy Stop/Sell Stop]
Symbol: ${symbol}
Price: [entry price or price range like 1.2345-1.2350]
SL: [stop loss level]
TP1: [first take profit]
TP2: [second take profit]
TP3: [third take profit]

Analysis: [Your detailed technical analysis explaining:
- Trend analysis (bullish/bearish/sideways)
- Key support/resistance levels
- Momentum indicators interpretation
- Entry rationale
- Risk management strategy
- Market context and timing]

ENHANCED ANALYSIS FACTORS:
- Market session (Asian/European/US overlap effects)
- Economic calendar events impact
- Volatility assessment
- Multiple timeframe confirmation
- Price action patterns (candlestick formations)
- Volume analysis if significant

Additional context: ${userPrompt}`;

    let analysis = '';
    let tradingSignal = null;

    if (aiProvider === 'gpt') {
      analysis = await analyzeWithGPT(systemPrompt, chartData, uploadedImage);
    } else if (aiProvider === 'gemini') {
      analysis = await analyzeWithGemini(systemPrompt, chartData, uploadedImage);
    } else {
      throw new Error('Invalid AI provider');
    }

    // Parse the analysis to extract trading signal
    tradingSignal = parseAnalysisForTradingSignal(analysis, chartData.symbol);

    return NextResponse.json({
      success: true,
      analysis,
      tradingSignal,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('AI analysis error:', error);
    return NextResponse.json(
      { 
        error: true, 
        message: (error as Error).message || 'AI analysis failed',
        details: String(error)
      },
      { status: 500 }
    );
  }
}

async function analyzeWithGPT(prompt: string, chartData: any, image?: string | null): Promise<string> {
  try {
    const apiKey = process.env.OPENAI_API_KEY || process.env.GPT_API_KEY;
    if (!apiKey) {
      throw new Error('OpenAI API key not found in environment variables');
    }

    // Prepare chart data summary for AI
    const latestData = chartData.data[chartData.data.length - 1];
    const previousData = chartData.data[chartData.data.length - 2];

    const chartSummary = `
Recent Price Action (${chartData.symbol} - ${chartData.timeframe}):
- Current Price: ${latestData?.close}
- Previous Close: ${previousData?.close}
- Price Change: ${latestData && previousData ? ((latestData.close - previousData.close) / previousData.close * 100).toFixed(2) : 'N/A'}%
- High: ${latestData?.high}
- Low: ${latestData?.low}
- Volume: ${latestData?.volume}

Technical Indicators:
- EMA 20: ${latestData?.ema_20}
- RSI 14: ${latestData?.rsi_14}
- RSI 25: ${latestData?.rsi_25}
- RSI 50: ${latestData?.rsi_50}
- MACD: ${latestData?.macd}
- MACD Signal: ${latestData?.macd_signal}
- SMA50 RSI25: ${latestData?.sma50_rsi25}
- SMA25 RSI50: ${latestData?.sma25_rsi50}

Historical Context (Last 5 bars):
${chartData.data.slice(-5).map((bar: any, i: number) =>
  `${i + 1}. Time: ${bar.time}, Close: ${bar.close}, RSI: ${bar.rsi_14}, MACD: ${bar.macd}`
).join('\n')}
`;

    const messages = [
      {
        role: "system",
        content: prompt
      },
      {
        role: "user",
        content: `${chartSummary}\n\nPlease analyze this chart data and provide trading recommendations in the specified format.`
      }
    ];

    // Add image if provided
    if (image) {
      messages.push({
        role: "user",
        content: `I've also uploaded a chart image for additional context: ${image.substring(0, 100)}...`
      });
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: messages,
        max_tokens: 1000,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || 'No analysis generated';

  } catch (error) {
    console.error('GPT Analysis Error:', error);
    // Fallback to mock analysis
    return generateMockAnalysis(chartData, 'GPT');
  }
}

async function analyzeWithGemini(prompt: string, chartData: any, image?: string | null): Promise<string> {
  try {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('Gemini API key not found in environment variables');
    }

    // Prepare chart data summary for AI
    const latestData = chartData.data[chartData.data.length - 1];
    const previousData = chartData.data[chartData.data.length - 2];

    const chartSummary = `
Market Analysis Request for ${chartData.symbol} (${chartData.timeframe}):

Current Market State:
- Price: ${latestData?.close}
- Change: ${latestData && previousData ? ((latestData.close - previousData.close) / previousData.close * 100).toFixed(2) : 'N/A'}%
- Range: ${latestData?.low} - ${latestData?.high}
- Volume: ${latestData?.volume}

Technical Indicators Analysis:
- EMA 20: ${latestData?.ema_20} (${latestData?.close > latestData?.ema_20 ? 'Above' : 'Below'} current price)
- RSI 14: ${latestData?.rsi_14} (${latestData?.rsi_14 > 70 ? 'Overbought' : latestData?.rsi_14 < 30 ? 'Oversold' : 'Neutral'})
- RSI 25: ${latestData?.rsi_25}
- RSI 50: ${latestData?.rsi_50}
- MACD: ${latestData?.macd} vs Signal: ${latestData?.macd_signal}
- MACD Trend: ${latestData?.macd > latestData?.macd_signal ? 'Bullish' : 'Bearish'}

Recent Price History:
${chartData.data.slice(-3).map((bar: any, i: number) =>
  `${i + 1}. ${bar.time}: ${bar.close} (RSI: ${bar.rsi_14})`
).join('\n')}
`;

    const requestBody = {
      contents: [{
        parts: [{
          text: `${prompt}\n\n${chartSummary}\n\nPlease provide a detailed technical analysis with specific trading recommendations.`
        }]
      }],
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1000,
      }
    };

    // Add image if provided
    if (image && image.startsWith('data:image/')) {
      const base64Data = image.split(',')[1];
      const mimeType = image.split(';')[0].split(':')[1];

      requestBody.contents[0].parts.push({
        inline_data: {
          mime_type: mimeType,
          data: base64Data
        }
      });
    }

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.candidates?.[0]?.content?.parts?.[0]?.text || 'No analysis generated';

  } catch (error) {
    console.error('Gemini Analysis Error:', error);
    // Fallback to mock analysis
    return generateMockAnalysis(chartData, 'Gemini');
  }
}

function generateMockAnalysis(chartData: any, provider: string): string {
  const latestData = chartData.data[chartData.data.length - 1];
  const previousData = chartData.data[chartData.data.length - 2];

  // Determine signal based on technical indicators
  const rsi = latestData?.rsi_14 || 50;
  const macdBullish = latestData?.macd > latestData?.macd_signal;
  const priceAboveEMA = latestData?.close > latestData?.ema_20;

  let signal = "Buy Limit";
  let priceMultiplier = 0.999;
  let slMultiplier = 0.995;
  let tp1Multiplier = 1.005;
  let tp2Multiplier = 1.010;
  let tp3Multiplier = 1.015;

  if (rsi > 70 || !macdBullish) {
    signal = "Sell Limit";
    priceMultiplier = 1.001;
    slMultiplier = 1.008;
    tp1Multiplier = 0.995;
    tp2Multiplier = 0.990;
    tp3Multiplier = 0.985;
  }

  return `${provider} AI Analysis for ${chartData.symbol}:

Signal: ${signal}
Symbol: ${chartData.symbol}
Price: ${(latestData?.close * priceMultiplier).toFixed(5)}-${(latestData?.close * (priceMultiplier + 0.002)).toFixed(5)}
SL: ${(latestData?.close * slMultiplier).toFixed(5)}
TP1: ${(latestData?.close * tp1Multiplier).toFixed(5)}
TP2: ${(latestData?.close * tp2Multiplier).toFixed(5)}
TP3: ${(latestData?.close * tp3Multiplier).toFixed(5)}

Analysis: Based on technical analysis of ${chartData.symbol} on ${chartData.timeframe} timeframe:

Current market conditions show ${macdBullish ? 'bullish' : 'bearish'} momentum with RSI at ${rsi} indicating ${rsi > 70 ? 'overbought' : rsi < 30 ? 'oversold' : 'neutral'} conditions. The MACD is showing ${macdBullish ? 'bullish' : 'bearish'} signals with MACD line ${macdBullish ? 'above' : 'below'} the signal line.

Price action is currently ${priceAboveEMA ? 'above' : 'below'} the EMA 20 at ${latestData?.ema_20}, which acts as dynamic ${priceAboveEMA ? 'support' : 'resistance'}.

Risk Management: Use proper position sizing (1-2% risk per trade) and monitor price action closely. Consider market volatility and fundamental news events that might affect ${chartData.symbol}.

Market Context: The ${chartData.timeframe} timeframe analysis suggests ${signal.includes('Buy') ? 'upward' : 'downward'} potential based on current technical setup.`;
}

function parseAnalysisForTradingSignal(analysis: string, symbol: string): any | null {
  const lines = analysis.split("\n").map((line) => line.trim());
  const rows: { tp: number; lot: number }[] = [];
  let data = {
    a: "Buy Limit",
    p: 0.0,
    sl: 0.0,
    s: symbol,
    c: "ZD_AI_ANALYSIS",
  };

  let hasValidData = false;

  lines.forEach((line) => {
    // TP Pattern
    if (line.toLowerCase().startsWith("tp")) {
      const match = line.match(/TP\d\s*:\s*(\d+(\.\d+)?)/i);
      if (match) {
        rows.push({ tp: parseFloat(match[1]), lot: 0.01 });
        hasValidData = true;
      }
    }

    // SL Pattern
    const slMatch = line.match(/SL\s*:\s*(\d+(\.\d+)?)/i);
    if (slMatch) {
      data.sl = parseFloat(slMatch[1]);
      hasValidData = true;
    }

    // Symbol Pattern
    const symbolMatch = line.match(/Symbol\s*:\s*(\w+)/i);
    if (symbolMatch) {
      data.s = symbolMatch[1].toUpperCase();
      hasValidData = true;
    }

    // Signal Pattern
    const signalMatch = line.match(/Signal\s*:\s*(.+)/i);
    if (signalMatch) {
      data.a = signalMatch[1].trim();
      hasValidData = true;
    }

    // Price Pattern
    const priceMatch = line.match(/Price\s*:\s*(\d+(\.\d+)?)[-–](\d+(\.\d+)?)/i);
    if (priceMatch) {
      const start = parseFloat(priceMatch[1]);
      const end = parseFloat(priceMatch[3]);
      const mid = (start + end) / 2;
      data.p = parseFloat(mid.toFixed(5));
      hasValidData = true;
    }
  });

  if (hasValidData) {
    return {
      rows: rows.length > 0 ? rows.slice(0, 10) : [{ tp: 0.0, lot: 0.01 }],
      ...data
    };
  }

  return null;
}
