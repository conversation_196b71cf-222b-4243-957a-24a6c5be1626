// app/api/ai-bots/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

interface AIBotConfig {
  id: string;
  name: string;
  enabled: boolean;
  aiProvider: "gpt" | "gemini";
  webhookUrl: string;
  autoPlaceOrder: boolean;
  scheduleCheck: boolean;
  checkInterval: number;
  createdAt: string;
  lastActive?: string;
}

const CONFIG_DIR = path.join(process.cwd(), 'config');
const AI_BOTS_CONFIG_FILE = path.join(CONFIG_DIR, 'ai-bots.json');
const LEGACY_AI_BOTS_CONFIG_FILE = path.join(process.cwd(), 'ai-bots.json');

// Ensure config directory exists
async function ensureConfigDir() {
  try {
    await fs.access(CONFIG_DIR);
  } catch {
    await fs.mkdir(CONFIG_DIR, { recursive: true });
  }
}

async function loadBots(): Promise<AIBotConfig[]> {
  try {
    await ensureConfigDir();

    // Try to load from config folder first
    try {
      const data = await fs.readFile(AI_BOTS_CONFIG_FILE, 'utf8');
      return JSON.parse(data);
    } catch {
      // Fallback to legacy location
      try {
        const data = await fs.readFile(LEGACY_AI_BOTS_CONFIG_FILE, 'utf8');
        const bots = JSON.parse(data);
        // Migrate to new location
        await saveBots(bots);
        // Remove legacy file
        await fs.unlink(LEGACY_AI_BOTS_CONFIG_FILE).catch(() => {});
        return bots;
      } catch {
        return [];
      }
    }
  } catch {
    return [];
  }
}

async function saveBots(bots: AIBotConfig[]): Promise<void> {
  await ensureConfigDir();
  await fs.writeFile(AI_BOTS_CONFIG_FILE, JSON.stringify(bots, null, 2));
}

export async function GET() {
  try {
    const bots = await loadBots();
    return NextResponse.json(bots);
  } catch (error) {
    console.error('Failed to load AI bots:', error);
    return NextResponse.json({ error: 'Failed to load AI bots' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const bots: AIBotConfig[] = await request.json();
    await saveBots(bots);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to save AI bots:', error);
    return NextResponse.json({ error: 'Failed to save AI bots' }, { status: 500 });
  }
}
