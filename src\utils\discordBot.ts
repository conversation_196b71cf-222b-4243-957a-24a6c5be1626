// utils/discordBot.ts
import { DiscordBotConfig, DiscordMessage } from '@/types/discord';

// Only import discord.js on server side
let Client: any, GatewayIntentBits: any;
if (typeof window === 'undefined') {
  try {
    const discord = require('discord.js');
    Client = discord.Client;
    GatewayIntentBits = discord.GatewayIntentBits;
  } catch (error) {
    console.warn('Discord.js not available:', error);
  }
}

class DiscordBotManager {
  private clients: Map<string, any> = new Map();
  private configs: Map<string, DiscordBotConfig> = new Map();

  async startBot(config: DiscordBotConfig, token: string): Promise<boolean> {
    try {
      if (this.clients.has(config.id)) {
        await this.stopBot(config.id);
      }

      const client = new Client({
        intents: [
          GatewayIntentBits.Guilds,
          GatewayIntentBits.GuildMessages,
          GatewayIntentBits.MessageContent,
        ],
      });

      client.on('ready', () => {
        console.log(`Discord bot ${config.name} is ready!`);
        this.updateBotStatus(config.id, true);
      });

      client.on('messageCreate', async (message: any) => {
        if (message.author.bot) return;
        if (message.guildId !== config.serverId) return;
        if (message.channelId !== config.channelId) return;

        const discordMessage: DiscordMessage = {
          id: message.id,
          content: message.content,
          author: {
            id: message.author.id,
            username: message.author.username,
            discriminator: message.author.discriminator,
          },
          timestamp: message.createdAt.toISOString(),
          channelId: message.channelId,
          serverId: message.guildId || '',
        };

        await this.processMessage(config, discordMessage);
      });

      client.on('error', (error: any) => {
        console.error(`Discord bot ${config.name} error:`, error);
        this.updateBotStatus(config.id, false);
      });

      await client.login(token);
      this.clients.set(config.id, client);
      this.configs.set(config.id, config);
      
      return true;
    } catch (error) {
      console.error(`Failed to start Discord bot ${config.name}:`, error);
      return false;
    }
  }

  async stopBot(botId: string): Promise<void> {
    const client = this.clients.get(botId);
    if (client) {
      await client.destroy();
      this.clients.delete(botId);
      this.configs.delete(botId);
      this.updateBotStatus(botId, false);
    }
  }

  private async processMessage(config: DiscordBotConfig, message: DiscordMessage): Promise<void> {
    try {
      // Use the parseLongText function logic here
      const parsedData = this.parseLongText(message.content);
      
      if (parsedData) {
        // Send to webhook
        await this.sendToWebhook(config.webhookUrl, parsedData);
      }
    } catch (error) {
      console.error(`Error processing message from bot ${config.name}:`, error);
    }
  }

  private parseLongText(content: string): any | null {
    // This mirrors the parseLongText function from WebhookTab2
    const lines = content.split("\n").map((line) => line.trim());
    const rows: { tp: number; lot: number }[] = [];
    let data = {
      a: "Buy Limit",
      p: 0.0,
      sl: 0.0,
      s: "XAUUSD",
      c: "ZD_INPUT",
      reason: "",
      risk: "",
    };

    let hasValidData = false;

    lines.forEach((line) => {
      if (line.toLowerCase().startsWith("tp")) {
        const match = line.match(/TP\d\s*:\s*(\d+(\.\d+)?)/i);
        if (match) {
          rows.push({ tp: parseFloat(match[1]), lot: 0.01 });
          hasValidData = true;
        }
      }

      const slMatch = line.match(/SL\s*:\s*(\d+(\.\d+)?)/i);
      if (slMatch) {
        data.sl = parseFloat(slMatch[1]);
        hasValidData = true;
      }

      const symbolMatch = line.match(/Symbol\s*:\s*(\w+)/i);
      if (symbolMatch) {
        data.s = symbolMatch[1].toUpperCase();
        hasValidData = true;
      }

      const signalMatch = line.match(/Signal\s*:\s*(.+)/i);
      if (signalMatch) {
        data.a = signalMatch[1].trim();
        hasValidData = true;
      }

      const priceMatch = line.match(/Price\s*:\s*(\d+(\.\d+)?)([-–](\d+(\.\d+)?))?/i);
      // const priceMatch = line.match(/Price\s*:\s*(\d+(\.\d+)?)[-–](\d+(\.\d+)?)/i);
      if (priceMatch) {
        const start = parseFloat(priceMatch[1]);
        const end = parseFloat(priceMatch[4]);
        // const mid = (start + end) / 2;
        if (!isNaN(start) && !isNaN(end)) {
          data.p = parseFloat(((start + end) / 2).toFixed(2));
        }else if(!isNaN(start)){
          data.p = parseFloat(start.toFixed(2));
        } 
        // data.p = parseFloat(mid.toFixed(2));
        hasValidData = true;
      }

      const commentMatch = line.match(/C\.(\w+)/i);
      if (commentMatch) {
        data.c = "ZD_" + commentMatch[1].slice(0, 3);
        hasValidData = true;
      }

      const reasonMatch = line.match(/Reason\s*:\s*(.+)/i);
      if (reasonMatch) {
        data.reason = reasonMatch[1].trim();
        hasValidData = true;
      }

      const riskMatch = line.match(/Risk\s*:\s*(.+)/i);
      if (riskMatch) {
        data.risk = riskMatch[1].trim();
        hasValidData = true;
      }
    });

    if (hasValidData) {
      return {
        rows: rows.length > 0 ? rows.slice(0, 10) : [{ tp: 0.0, lot: 0.01 }],
        ...data
      };
    }

    return null;
  }

  private async sendToWebhook(webhookUrl: string, data: any): Promise<void> {
    try {
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Webhook request failed: ${response.status}`);
      }

      console.log('Successfully sent data to webhook:', webhookUrl);
    } catch (error) {
      console.error('Failed to send data to webhook:', error);
      throw error;
    }
  }

  private updateBotStatus(botId: string, isActive: boolean): void {
    // This would typically update a database or state management
    // For now, we'll just log the status change
    console.log(`Bot ${botId} status updated: ${isActive ? 'active' : 'inactive'}`);
  }

  getBotStatus(botId: string): boolean {
    return this.clients.has(botId) && this.clients.get(botId)?.isReady() === true;
  }

  getAllBots(): DiscordBotConfig[] {
    return Array.from(this.configs.values());
  }

  generateInviteLink(clientId: string, permissions: string[]): string {
    const permissionMap: Record<string, string> = {
      'VIEW_CHANNEL': '1024',
      'READ_MESSAGE_HISTORY': '65536',
      'SEND_MESSAGES': '2048',
      'MANAGE_MESSAGES': '8192',
      'EMBED_LINKS': '16384',
      'ATTACH_FILES': '32768',
      'USE_EXTERNAL_EMOJIS': '262144',
      'ADD_REACTIONS': '64',
      'MENTION_EVERYONE': '131072',
      'MANAGE_WEBHOOKS': '536870912',
      'USE_SLASH_COMMANDS': '2147483648'
    };

    let permissionValue = 0;
    permissions.forEach(permission => {
      if (permissionMap[permission]) {
        permissionValue += parseInt(permissionMap[permission]);
      }
    });

    return `https://discord.com/api/oauth2/authorize?client_id=${clientId}&permissions=${permissionValue}&scope=bot`;
  }
}

export const discordBotManager = new DiscordBotManager();
