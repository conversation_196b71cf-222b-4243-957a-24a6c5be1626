// app/api/ai-analysis/route.ts
import { NextRequest, NextResponse } from 'next/server';

const WEBHOOK_URL = process.env.NEXT_PUBLIC_WEBHOOK_URL_AI_ANALYSIS;

interface AIAnalysisRequest {
  symbol: string;
  timeframes: string[];
  barback: number;
  prompt: string;
  ai: string;
  image: string;
  use_signal_format: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const data: AIAnalysisRequest = await request.json();

    const webhookUrl = WEBHOOK_URL;

    if (!webhookUrl) {
      return NextResponse.json(
        { error: true, message: 'AI Analysis webhook URL not configured' },
        { status: 500 }
      );
    }

    console.log(`Proxying AI analysis request to: ${webhookUrl}`);
    console.log('Data:', JSON.stringify(data, null, 2));

    // Get access token from environment variables
    const accessToken = process.env.WEBHOOK_ACCESS_TOKEN;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'NextJS-AI-Analysis-Proxy/1.0',
      'Accept': 'application/json',
    };

    // Add access token to headers if available
    if (accessToken) {
      headers['Authorization'] = `Bearer ${accessToken}`;
      headers['X-Access-Token'] = accessToken;
    }

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        ...data,
        // Add metadata
        _meta: {
          source: 'web_app',
          timestamp: new Date().toISOString(),
          accessToken: accessToken ? 'provided' : 'not_provided'
        }
      }),
    });

    const responseText = await response.text();
    let responseData;

    // Log the raw response for debugging
    console.log(`Response status: ${response.status}`);
    console.log(`Response headers:`, Object.fromEntries(response.headers.entries()));
    console.log(`Raw response:`, responseText.substring(0, 500)); // First 500 chars

    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = {
        message: responseText,
        isHtml: responseText.trim().startsWith('<!doctype') || responseText.trim().startsWith('<html')
      };
    }

    // If the webhook server returned an error status, mark as unsuccessful
    const success = response.ok && !responseText.includes('Internal Server Error');

    // Log error details if webhook failed
    if (!success) {
      console.error('Webhook Error Details:');
      console.error('- Status:', response.status);
      console.error('- Status Text:', response.statusText);
      console.error('- Response Type:', response.headers.get('content-type'));
      console.error('- Is HTML Error:', responseData.isHtml);
      console.error('- Full Response:', responseText);
    }

    return NextResponse.json({
      success,
      status: response.status,
      statusText: response.statusText,
      data: responseData,
      webhookUrl, // Include for debugging
      error: !success ? {
        type: response.status === 500 ? 'webhook_internal_error' : 'webhook_error',
        message: `Python webhook returned ${response.status}: ${response.statusText}`,
        details: responseData.isHtml ? 'HTML error page returned' : responseData.message,
        isHtmlError: responseData.isHtml
      } : undefined
    });

  } catch (error: unknown) {
    console.error('AI analysis proxy error:', error);
    return NextResponse.json(
      {
        error: true,
        message: (error as Error).message || 'Internal server error',
        details: String(error)
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Access-Token',
    },
  });
}