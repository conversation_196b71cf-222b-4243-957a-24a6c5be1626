import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

const CONFIG_DIR = path.join(process.cwd(), 'config');
const ROOT_DIR = process.cwd();

// Files to migrate to config folder
const FILES_TO_MIGRATE = [
  'ai-analysis-history.json',
  'ai-bots.json',
  'discord-bots.json'
];

// Ensure config directory exists
async function ensureConfigDir() {
  try {
    await fs.access(CONFIG_DIR);
  } catch {
    await fs.mkdir(CONFIG_DIR, { recursive: true });
  }
}

export async function POST() {
  try {
    await ensureConfigDir();
    
    const migrationResults = [];
    
    for (const fileName of FILES_TO_MIGRATE) {
      const sourcePath = path.join(ROOT_DIR, fileName);
      const targetPath = path.join(CONFIG_DIR, fileName);
      
      try {
        // Check if source file exists
        await fs.access(sourcePath);
        
        // Check if target file already exists
        try {
          await fs.access(targetPath);
          migrationResults.push({
            file: fileName,
            status: 'skipped',
            message: 'File already exists in config folder'
          });
          continue;
        } catch {
          // Target doesn't exist, proceed with migration
        }
        
        // Read source file
        const data = await fs.readFile(sourcePath, 'utf8');
        
        // Write to target location
        await fs.writeFile(targetPath, data);
        
        // Remove source file
        await fs.unlink(sourcePath);
        
        migrationResults.push({
          file: fileName,
          status: 'migrated',
          message: 'Successfully moved to config folder'
        });
        
      } catch (error) {
        if ((error as any).code === 'ENOENT') {
          migrationResults.push({
            file: fileName,
            status: 'not_found',
            message: 'Source file does not exist'
          });
        } else {
          migrationResults.push({
            file: fileName,
            status: 'error',
            message: `Migration failed: ${(error as Error).message}`
          });
        }
      }
    }
    
    return NextResponse.json({
      success: true,
      results: migrationResults
    });
    
  } catch (error) {
    console.error('Error during migration:', error);
    return NextResponse.json(
      { error: 'Migration failed', details: (error as Error).message },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    await ensureConfigDir();
    
    const status = [];
    
    for (const fileName of FILES_TO_MIGRATE) {
      const sourcePath = path.join(ROOT_DIR, fileName);
      const targetPath = path.join(CONFIG_DIR, fileName);
      
      const sourceExists = await fs.access(sourcePath).then(() => true).catch(() => false);
      const targetExists = await fs.access(targetPath).then(() => true).catch(() => false);
      
      status.push({
        file: fileName,
        sourceExists,
        targetExists,
        needsMigration: sourceExists && !targetExists
      });
    }
    
    return NextResponse.json({
      configDir: CONFIG_DIR,
      files: status
    });
    
  } catch (error) {
    console.error('Error checking migration status:', error);
    return NextResponse.json(
      { error: 'Failed to check migration status' },
      { status: 500 }
    );
  }
}
